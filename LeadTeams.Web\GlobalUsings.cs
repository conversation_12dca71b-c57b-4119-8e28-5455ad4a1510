﻿global using ClosedXML.Excel;
global using GMCadiomCore.Authentications.PermissionsAndSessions;
global using GMCadiomCore.Models.BaseModels;
global using GMCadiomCore.Models.Enumerations;
global using GMCadiomCore.Models.Model;
global using GMCadiomCore.Models.ViewModel;
global using GMCadiomCore.Shared.Extensions;
global using GMCadiomCore.Shared.Helper;
global using LeadTeams.Models.Enumerations;
global using LeadTeams.Models.Model;
global using LeadTeams.Models.ModelDTO.Authentication;
global using LeadTeams.Models.ModelDTO.Reports;
global using LeadTeams.Models.ViewModel.Allowance;
global using LeadTeams.Models.ViewModel.AskLeave;
global using LeadTeams.Models.ViewModel.BaseModels;
global using LeadTeams.Models.ViewModel.Employee;
global using LeadTeams.Models.ViewModel.EmployeeEducationalQualification;
global using LeadTeams.Models.ViewModel.EmployeeKids;
global using LeadTeams.Models.ViewModel.EmployeeManager;
global using LeadTeams.Models.ViewModel.ManagementTeam;
global using LeadTeams.Models.ViewModel.Meeting;
global using LeadTeams.Models.ViewModel.Project;
global using LeadTeams.Models.ViewModel.ScreensAccessProfile;
global using LeadTeams.Models.ViewModel.ScreensAccessProfileDetails;
global using LeadTeams.Models.ViewModel.ScreenShotsMonitoring;
global using LeadTeams.Models.ViewModel.Setting;
global using LeadTeams.Models.ViewModel.Shift;
global using LeadTeams.Models.ViewModel.ShiftDynamicPattern;
global using LeadTeams.Models.ViewModel.ShiftFixedPattern;
global using LeadTeams.Models.ViewModel.Task;
global using LeadTeams.PermissionAndSession;
global using LeadTeams.PermissionAndSession.Authentication;
global using LeadTeams.PermissionAndSession.Authentication.Extensions;
global using LeadTeams.PermissionAndSession.DI;
global using LeadTeams.PermissionAndSession.Session;
global using LeadTeams.Services.API.DI;
global using LeadTeams.Services.Core.Authentication;
global using LeadTeams.Services.Core.BaseService;
global using LeadTeams.Services.Core.Business;
global using LeadTeams.Services.Core.Reports;
global using LeadTeams.Shared.Helper;
global using LeadTeams.Web.Attributes;
global using LeadTeams.Web.Helpers;
global using LeadTeams.Web.Helpers.Extensions;
global using LeadTeams.Web.Middlewares;
global using LeadTeams.Web.Models;
global using LeadTeams.Web.ViewModels.Allowance;
global using LeadTeams.Web.ViewModels.AskLeave;
global using LeadTeams.Web.ViewModels.Base;
global using LeadTeams.Web.ViewModels.Employee;
global using LeadTeams.Web.ViewModels.ManagementTeam;
global using LeadTeams.Web.ViewModels.Meeting;
global using LeadTeams.Web.ViewModels.Project;
global using LeadTeams.Web.ViewModels.Reports;
global using LeadTeams.Web.ViewModels.ScreensAccessProfile;
global using LeadTeams.Web.ViewModels.ScreenShotsMonitoring;
global using LeadTeams.Web.ViewModels.Setting;
global using LeadTeams.Web.ViewModels.Shift;
global using LeadTeams.Web.ViewModels.Task;
global using Microsoft.AspNetCore.Authorization;
global using Microsoft.AspNetCore.Mvc;
global using Microsoft.AspNetCore.Mvc.Filters;
global using Microsoft.AspNetCore.Mvc.ModelBinding;
global using Microsoft.AspNetCore.Mvc.Rendering;
global using System.Collections;
global using System.Diagnostics;
global using System.IdentityModel.Tokens.Jwt;
global using System.Reflection;
global using System.Text;
