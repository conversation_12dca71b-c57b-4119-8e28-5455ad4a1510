﻿namespace LeadTeams.Services.Server.Business
{
    public class ManagementTeamService : LeadTeamsBaseService<ManagementTeamModel, ManagementTeamModel, CreateManagementTeamViewModel, UpdateManagementTeamViewModel>, IManagementTeamService
    {
        private readonly IUnitOfWork _unitOfWork;

        public ManagementTeamService(IAuthenticationValidationService authenticationValidationService, IUnitOfWork unitOfWork) : base(authenticationValidationService, unitOfWork.ManagementTeam)
        {
            _unitOfWork = unitOfWork;
        }

        protected override Func<IQueryable<ManagementTeamModel>, IIncludableQueryable<ManagementTeamModel, object>> Includes =>
            x => x
            .Include(xx => xx.ManagementTeamEmployees)
            .Include(xx => xx.ManagementTeamManagers);

        public List<EmployeeModel> SelectiveTeamManagerList() => _unitOfWork.Employee.GetAll();
        public List<EmployeeModel> SelectiveTeamEmployeeList() => _unitOfWork.Employee.GetAll();

        public List<EmployeeModel> GetTeamManagersForEmployee(Ulid employeeId) => _unitOfWork.ManagementTeamManager.GetAsEmployees(employeeId);
        public List<EmployeeModel> GetTeamEmployeesForEmployee(Ulid employeeId) => _unitOfWork.ManagementTeamEmployee.GetAsEmployees(employeeId);
    }
}
