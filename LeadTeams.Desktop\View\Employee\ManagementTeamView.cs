﻿namespace LeadTeams.Desktop.View
{
    public partial class ManagementTeamView : GenericViewTable.GenericManagementTeamView
    {
        private readonly IManagementTeamService _managementTeamService;

        //Constructor
        public ManagementTeamView(IManagementTeamService managementTeamService, ManagementTeamModel? editModel = null) : base(managementTeamService)
        {
            InitializeComponent();

            _managementTeamService = managementTeamService;

            AssociateAndRaiseEvents();
            LoadAllLists();
            ClearAndNew();

            if (editModel != null)
                SetUpdateEntity(editModel.ToUpdateDto());

            DeleteTeamManagerRow.Text = "Delete Row";
            DeleteTeamManagerRow.UseColumnTextForButtonValue = true;
            DeleteTeamEmployeeRow.Text = "Delete Row";
            DeleteTeamEmployeeRow.UseColumnTextForButtonValue = true;
        }

        private void AssociateAndRaiseEvents()
        {
            cmbTeamManagerId.SelectedValueChanged += (s, e) =>
            {
                if (cmbTeamManagerId.SelectedItem != null)
                {
                    EmployeeModel? employee = cmbTeamManagerId.SelectedItem as EmployeeModel;
                    if (employee != null)
                    {
                        lblTeamManagerName.Text = string.IsNullOrEmpty(employee.EmployeeName) ? "....." : employee.EmployeeName;
                        lblTeamManagerContact.Text = string.IsNullOrEmpty(employee.EmployeeContact) ? "....." : employee.EmployeeContact;
                        lblTeamManagerJobTitle.Text = string.IsNullOrEmpty(employee.EmployeeJobTitle) ? "....." : employee.EmployeeJobTitle;
                    }
                    else
                    {
                        lblTeamManagerName.Text = ".....";
                        lblTeamManagerContact.Text = ".....";
                        lblTeamManagerJobTitle.Text = ".....";
                    }
                }
                else
                {
                    lblTeamManagerName.Text = ".....";
                    lblTeamManagerContact.Text = ".....";
                    lblTeamManagerJobTitle.Text = ".....";
                }
            };
            dgvTeamManager.CellDoubleClick += (s, e) =>
            {
                if (e.RowIndex == dgvTeamManager.NewRowIndex || e.RowIndex < 0)
                    return;
                else
                {
                    if (teamManagerModelBindingSource.Current is EmployeeModel teamManager and not null)
                    {
                        cmbTeamManagerId.SelectedValue = teamManager.Id;

                        dgvTeamManager.Enabled = false;
                        btnAddTeamManagerRow.Text = "Update";
                    }
                }
            };
            dgvTeamManager.CellClick += (s, e) =>
            {
                if (e.RowIndex == dgvTeamManager.NewRowIndex || e.RowIndex < 0)
                    return;

                if (e.ColumnIndex == dgvTeamManager.Columns[nameof(DeleteTeamManagerRow)].Index)
                {
                    dgvTeamManager.Rows.RemoveAt(e.RowIndex);
                    return;
                }
            };
            btnAddTeamManagerRow.Click += (s, e) =>
            {
                var lst = teamManagerModelBindingSource.List.OfType<EmployeeModel>();
                EmployeeModel? teamManagerModel = cmbTeamManagerId.SelectedItem as EmployeeModel;

                if (teamManagerModel is null)
                    return;

                if (btnAddTeamManagerRow.Text == "Update")
                {
                    if (teamManagerModelBindingSource.Current is EmployeeModel teamManager and not null)
                    {
                        if (lst.Any(x => x.Id == teamManagerModel.Id) && teamManager.Id != teamManagerModel.Id)
                            teamManagerModelBindingSource.RemoveCurrent();
                        else
                            teamManagerModelBindingSource[teamManagerModelBindingSource.Position] = teamManagerModel;

                        dgvTeamManager.Enabled = true;
                        btnAddTeamManagerRow.Text = "Add";
                        teamManagerModelBindingSource.ResetCurrentItem();
                        teamManagerModelBindingSource.ResetBindings(false);
                    }
                }
                else if (btnAddTeamManagerRow.Text == "Add")
                {
                    if (!lst.Any(x => x.Id == teamManagerModel.Id))
                    {
                        teamManagerModelBindingSource.Add(teamManagerModel);
                        teamManagerModelBindingSource.ResetBindings(false);
                    }
                }

                cmbTeamManagerId.SelectedValue = 0;
            };

            cmbTeamEmployeeId.SelectedValueChanged += (s, e) =>
            {
                if (cmbTeamEmployeeId.SelectedItem != null)
                {
                    EmployeeModel? employee = cmbTeamEmployeeId.SelectedItem as EmployeeModel;
                    if (employee != null)
                    {
                        lblTeamEmployeeName.Text = string.IsNullOrEmpty(employee.EmployeeName) ? "....." : employee.EmployeeName;
                        lblTeamEmployeeContact.Text = string.IsNullOrEmpty(employee.EmployeeContact) ? "....." : employee.EmployeeContact;
                        lblTeamEmployeeJobTitle.Text = string.IsNullOrEmpty(employee.EmployeeJobTitle) ? "....." : employee.EmployeeJobTitle;
                    }
                    else
                    {
                        lblTeamEmployeeName.Text = ".....";
                        lblTeamEmployeeContact.Text = ".....";
                        lblTeamEmployeeJobTitle.Text = ".....";
                    }
                }
                else
                {
                    lblTeamEmployeeName.Text = ".....";
                    lblTeamEmployeeContact.Text = ".....";
                    lblTeamEmployeeJobTitle.Text = ".....";
                }
            };
            dgvTeamEmployee.CellDoubleClick += (s, e) =>
            {
                if (e.RowIndex == dgvTeamEmployee.NewRowIndex || e.RowIndex < 0)
                    return;
                else
                {
                    if (teamEmployeeModelBindingSource.Current is EmployeeModel teamEmployee and not null)
                    {
                        cmbTeamEmployeeId.SelectedValue = teamEmployee.Id;

                        dgvTeamEmployee.Enabled = false;
                        btnAddTeamEmployeeRow.Text = "Update";
                    }
                }
            };
            dgvTeamEmployee.CellClick += (s, e) =>
            {
                if (e.RowIndex == dgvTeamEmployee.NewRowIndex || e.RowIndex < 0)
                    return;

                if (e.ColumnIndex == dgvTeamEmployee.Columns[nameof(DeleteTeamEmployeeRow)].Index)
                {
                    dgvTeamEmployee.Rows.RemoveAt(e.RowIndex);
                    return;
                }
            };
            btnAddTeamEmployeeRow.Click += (s, e) =>
            {
                var lst = teamEmployeeModelBindingSource.List.OfType<EmployeeModel>();
                EmployeeModel? teamEmployeeModel = cmbTeamEmployeeId.SelectedItem as EmployeeModel;

                if (teamEmployeeModel is null)
                    return;

                if (btnAddTeamEmployeeRow.Text == "Update")
                {
                    if (teamEmployeeModelBindingSource.Current is EmployeeModel teamEmployee and not null)
                    {
                        if (lst.Any(x => x.Id == teamEmployeeModel.Id) && teamEmployee.Id != teamEmployeeModel.Id)
                            teamEmployeeModelBindingSource.RemoveCurrent();
                        else
                            teamEmployeeModelBindingSource[teamEmployeeModelBindingSource.Position] = teamEmployeeModel;

                        dgvTeamEmployee.Enabled = true;
                        btnAddTeamEmployeeRow.Text = "Add";
                        teamEmployeeModelBindingSource.ResetCurrentItem();
                        teamEmployeeModelBindingSource.ResetBindings(false);
                    }
                }
                else if (btnAddTeamEmployeeRow.Text == "Add")
                {
                    if (!lst.Any(x => x.Id == teamEmployeeModel.Id))
                    {
                        teamEmployeeModelBindingSource.Add(teamEmployeeModel);
                        teamEmployeeModelBindingSource.ResetBindings(false);
                    }
                }

                cmbTeamEmployeeId.SelectedValue = 0;
            };
        }

        protected override void ClearAndNew()
        {
            base.ClearAndNew();

            txtTeamName.TextValue = string.Empty;
            TeamEmployees = new List<EmployeeModel>();
            TeamManagers = new List<EmployeeModel>();
        }

        protected override void LoadAllLists()
        {
            base.LoadAllLists();

            cmbTeamManagerId.FillComboBox<EmployeeModel>(x => x.EmployeeName, x => x.Id, _managementTeamService.SelectiveTeamManagerList());
            cmbTeamEmployeeId.FillComboBox<EmployeeModel>(x => x.EmployeeName, x => x.Id, _managementTeamService.SelectiveTeamEmployeeList());
        }

        //Keys
        public ICollection<EmployeeModel> TeamEmployees
        {
            get => teamEmployeeModelBindingSource.List.OfType<EmployeeModel>().ToList();
            set => teamEmployeeModelBindingSource.DataSource = value;
        }
        public ICollection<EmployeeModel> TeamManagers
        {
            get => teamManagerModelBindingSource.List.OfType<EmployeeModel>().ToList();
            set => teamManagerModelBindingSource.DataSource = value;
        }

        protected override CreateManagementTeamViewModel CreateEntity()
        {
            CreateManagementTeamViewModel model = new CreateManagementTeamViewModel()
            {
                TeamName = txtTeamName.TextValue,

                SelectedManagementTeamEmployees = TeamEmployees.Select(x => x.Id).ToList(),
                SelectedManagementTeamManagers = TeamManagers.Select(x => x.Id).ToList(),
                OrganizationId = LocalSessionStatic.LoginSession.Employee.Organization.Id,
            };
            return model;
        }

        protected override void SetCreateEntity(CreateManagementTeamViewModel model)
        {
            base.SetCreateEntity(model);

            txtTeamName.TextValue = model.TeamName;

            TeamManagers = _managementTeamService.GetTeamManagersForEmployee(Id);
            TeamEmployees = _managementTeamService.GetTeamEmployeesForEmployee(Id);
        }

        protected override UpdateManagementTeamViewModel UpdateEntity()
        {
            UpdateManagementTeamViewModel model = new UpdateManagementTeamViewModel()
            {
                Id = Id,
                TeamName = txtTeamName.TextValue,

                SelectedManagementTeamEmployees = TeamEmployees.Select(x => x.Id).ToList(),
                SelectedManagementTeamManagers = TeamManagers.Select(x => x.Id).ToList(),
                OrganizationId = LocalSessionStatic.LoginSession.Employee.Organization.Id,
            };
            return model;
        }

        protected override void SetUpdateEntity(UpdateManagementTeamViewModel model)
        {
            base.SetUpdateEntity(model);

            txtTeamName.TextValue = model.TeamName;

            TeamManagers = _managementTeamService.GetTeamManagersForEmployee(model.Id);
            TeamEmployees = _managementTeamService.GetTeamEmployeesForEmployee(model.Id);
        }
    }
}
