﻿namespace LeadTeams.Models.ModelMapper
{
    public static class ManagementTeamMapper
    {
        public static CreateManagementTeamViewModel ToCreateDto(this ManagementTeamModel entity)
        {
            CreateManagementTeamViewModel viewModel = new CreateManagementTeamViewModel()
            {
                TeamName = entity.TeamName,
                SelectedManagementTeamManagers = entity.ManagementTeamManagers.Select(x => x.Id).ToList(),
                SelectedManagementTeamEmployees = entity.ManagementTeamEmployees.Select(x => x.Id).ToList(),
                OrganizationId = entity.OrganizationId,
            };
            return viewModel;
        }

        public static ManagementTeamModel ToEntity(this CreateManagementTeamViewModel entity)
        {
            ManagementTeamModel model = new ManagementTeamModel()
            {
                TeamName = entity.TeamName,
                ManagementTeamManagers = entity.SelectedManagementTeamManagers.Select(x => new ManagementTeamManagerModel()
                {
                    ManagerId = x,
                    OrganizationId = entity.OrganizationId,
                }).ToList(),
                ManagementTeamEmployees = entity.SelectedManagementTeamEmployees.Select(x => new ManagementTeamEmployeeModel()
                {
                    EmployeeId = x,
                    OrganizationId = entity.OrganizationId,
                }).ToList(),
                OrganizationId = entity.OrganizationId,
            };
            return model;
        }

        public static UpdateManagementTeamViewModel ToUpdateDto(this ManagementTeamModel entity)
        {
            UpdateManagementTeamViewModel viewModel = new UpdateManagementTeamViewModel()
            {
                Id = entity.Id,
                TeamName = entity.TeamName,
                SelectedManagementTeamManagers = entity.ManagementTeamManagers.Select(x => x.Id).ToList(),
                SelectedManagementTeamEmployees = entity.ManagementTeamEmployees.Select(x => x.Id).ToList(),
                OrganizationId = entity.OrganizationId,
            };
            return viewModel;
        }

        public static ManagementTeamModel ToEntity(this UpdateManagementTeamViewModel entity)
        {
            ManagementTeamModel model = new ManagementTeamModel()
            {
                Id = entity.Id,
                TeamName = entity.TeamName,
                ManagementTeamManagers = entity.SelectedManagementTeamManagers.Select(x => new ManagementTeamManagerModel()
                {
                    ManagementTeamId = entity.Id,
                    ManagerId = x,
                    OrganizationId = entity.OrganizationId,
                }).ToList(),
                ManagementTeamEmployees = entity.SelectedManagementTeamEmployees.Select(x => new ManagementTeamEmployeeModel()
                {
                    ManagementTeamId = entity.Id,
                    EmployeeId = x,
                    OrganizationId = entity.OrganizationId,
                }).ToList(),
                OrganizationId = entity.OrganizationId,
            };
            return model;
        }
    }
}
