namespace LeadTeams.Web.Controllers.Business
{
    public class ManagementTeamController : BaseBusinessController<
        ManagementTeamModel,
        ManagementTeamModel,
        CreateManagementTeamViewModel,
        UpdateManagementTeamViewModel,
        IndexManagementTeamFormViewModel,
        CreateManagementTeamFormViewModel,
        UpdateManagementTeamFormViewModel>
    {
        private readonly IManagementTeamService _managementTeamService;

        public ManagementTeamController(IManagementTeamService managementTeamService) : base(managementTeamService)
        {
            _managementTeamService = managementTeamService;
        }

        public override async Task<IActionResult> Create(CreateManagementTeamFormViewModel model)
        {
            if (!ModelState.IsValid)
                return await FullFillViewAsync(model);

            ManagementTeamModel? managementTeam = await _managementTeamService.AddAsync(model);

            if (managementTeam is null)
                return await FullFillViewAsync(model);

            return RedirectToAction(nameof(Index));
        }

        public override async Task<IActionResult> Update(Ulid id)
        {
            ManagementTeamModel? entity = await _managementTeamService.GetByIdAsync(id);

            if (entity is null)
                return NotFound();

            UpdateManagementTeamFormViewModel viewModel = new UpdateManagementTeamFormViewModel()
            {
                Id = entity.Id,
                TeamName = entity.TeamName,
                SelectedManagementTeamEmployees = entity.ManagementTeamEmployees.Select(x => x.Id).ToList(),
                SelectedManagementTeamManagers = entity.ManagementTeamManagers.Select(x => x.Id).ToList(),
                Employees = _managementTeamService.SelectiveTeamEmployeeList(),
            };

            return View(viewModel);
        }

        public override async Task<IActionResult> Update(UpdateManagementTeamFormViewModel model)
        {
            if (!ModelState.IsValid)
                return await FailedActionResultAsync(model);

            ManagementTeamModel? managementTeam = await _managementTeamService.UpdateAsync(model);

            if (managementTeam is null)
                return await FailedActionResultAsync(model);

            return RedirectToAction(nameof(Index));
        }

        protected override async Task<IActionResult> FullFillViewAsync(CreateManagementTeamFormViewModel model)
        {
            model.Employees = _managementTeamService.SelectiveTeamEmployeeList();
            return await Task.FromResult(View(model));
        }

        protected override async Task<IActionResult> FailedActionResultAsync(UpdateManagementTeamFormViewModel model)
        {
            model.Employees = _managementTeamService.SelectiveTeamEmployeeList();
            return await Task.FromResult(View(model));
        }
    }
}
