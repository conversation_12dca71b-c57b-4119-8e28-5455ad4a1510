﻿namespace LeadTeams.Models.ViewModel.ManagementTeam
{
    public class CreateManagementTeamViewModel : BaseOrganizationCreateViewModel, IEntityMapper<ManagementTeamModel, CreateManagementTeamViewModel>
    {
        [CustomRequired]
        [DisplayName("Team Name")]
        [MaxLength(100)]
        public string TeamName { get; set; }

        [CustomRequired]
        [DisplayName("Management Team Employees")]
        public ICollection<Ulid> SelectedManagementTeamEmployees { get; set; } = new List<Ulid>();

        [CustomRequired]
        [DisplayName("Management Team Managers")]
        public ICollection<Ulid> SelectedManagementTeamManagers { get; set; } = new List<Ulid>();

        public CreateManagementTeamViewModel ToDto(ManagementTeamModel entity) => entity.ToCreateDto();

        public ManagementTeamModel ToEntity() => ManagementTeamMapper.ToEntity(this);
    }
}
