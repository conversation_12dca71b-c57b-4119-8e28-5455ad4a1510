﻿namespace LeadTeams.Models.ViewModel.ManagementTeam
{
    public class UpdateManagementTeamViewModel : BaseOrganizationUpdateViewModel, IEntityMapper<ManagementTeamModel, UpdateManagementTeamViewModel>
    {
        [CustomRequired]
        [DisplayName("Team Name")]
        [MaxLength(100)]
        public string TeamName { get; set; }

        [CustomRequired]
        [Display<PERSON><PERSON>("Management Team Employees")]
        public ICollection<Ulid> SelectedManagementTeamEmployees { get; set; } = new List<Ulid>();

        [CustomRequired]
        [DisplayName("Management Team Managers")]
        public ICollection<Ulid> SelectedManagementTeamManagers { get; set; } = new List<Ulid>();

        public UpdateManagementTeamViewModel ToDto(ManagementTeamModel entity) => entity.ToUpdateDto();

        public ManagementTeamModel ToEntity() => ManagementTeamMapper.ToEntity(this);
    }
}
